#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时任务处理器
基于定时器轮询的任务处理模式，更适合高并发场景
"""

import time
import os
import json
import threading
import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Callable
import traceback

from task_manager import TaskManager
from shipment_manager import ShipmentManager
from task_executor import run_visual_agent
from scraping_executor import ScrapingExecutor
from ai_analysis_executor import AIAnalysisExecutor


class ScheduledTaskProcessor:
    """
    基于定时器的任务处理器
    每个阶段独立处理，支持高并发
    """
    
    def __init__(self, 
                 scraping_interval: int = 10,    # 网页抓取检查间隔（秒）
                 ai_interval: int = 5,           # AI分析检查间隔（秒）  
                 status_update_interval: int = 30, # 状态更新间隔（秒）
                 max_scraping_tasks: int = 2,    # 最大并发抓取任务
                 max_ai_tasks: int = 3,          # 最大并发AI分析任务
                 completion_callback: Callable = None):
        """
        初始化定时任务处理器
        
        Args:
            scraping_interval: 网页抓取任务检查间隔
            ai_interval: AI分析任务检查间隔
            status_update_interval: 状态更新检查间隔
            max_scraping_tasks: 最大并发网页抓取任务数
            max_ai_tasks: 最大并发AI分析任务数
            completion_callback: 任务完成回调函数
        """
        self.task_manager = TaskManager()
        self.shipment_manager = ShipmentManager()
        
        # 配置参数
        self.scraping_interval = scraping_interval
        self.ai_interval = ai_interval
        self.status_update_interval = status_update_interval
        self.max_scraping_tasks = max_scraping_tasks
        self.max_ai_tasks = max_ai_tasks
        self.completion_callback = completion_callback
        
        # 运行状态
        self.running = False
        
        # 活跃任务跟踪
        self.active_scraping_tasks = {}
        self.active_ai_tasks = {}
        
        # 执行器
        self.scraping_executor = ScrapingExecutor()
        self.ai_executor = AIAnalysisExecutor()
        
        # 定时器
        self.scraping_timer = None
        self.ai_timer = None
        self.status_timer = None
    
    def start(self):
        """启动定时任务处理器"""
        if self.running:
            print("[WARNING] 定时任务处理器已在运行中")
            return
        
        self.running = True
        print("[START] 启动定时任务处理器...")
        
        # 启动网页抓取定时器
        self.scraping_timer = threading.Timer(self.scraping_interval, self._scraping_task_handler)
        self.scraping_timer.daemon = True
        self.scraping_timer.start()
        print(f"[START] 网页抓取定时器已启动 (间隔: {self.scraping_interval}秒)")
        
        # 不再自动启动AI分析定时器 - 改为按需启动
        print("[INFO] AI分析将在网页抓取完成后自动触发")
        
        # 启动时检查并处理已有的pending AI分析任务
        self._process_existing_pending_ai_tasks()
        
        # 启动状态更新定时器
        self.status_timer = threading.Timer(self.status_update_interval, self._status_update_handler)
        self.status_timer.daemon = True
        self.status_timer.start()
        print(f"[START] 状态更新定时器已启动 (间隔: {self.status_update_interval}秒)")
        
        print("[SUCCESS] 定时任务处理器启动完成")
        # 写入一次心跳，标记已启动
        try:
            self._write_heartbeat(running=True, status_msg="started")
        except Exception:
            pass
    
    def _process_existing_pending_ai_tasks(self):
        """处理启动时已存在的pending AI分析任务"""
        try:
            print("[STARTUP] 检查已有的pending AI分析任务...")
            
            # 获取所有pending的AI分析任务
            pending_ai_tasks = self.task_manager.get_pending_tasks_by_stage('ai_analysis', limit=self.max_ai_tasks)
            
            if pending_ai_tasks:
                print(f"[STARTUP] 发现 {len(pending_ai_tasks)} 个pending AI分析任务，立即处理")
                
                for task in pending_ai_tasks:
                    if len(self.active_ai_tasks) >= self.max_ai_tasks:
                        break
                    
                    task_id = task['id']
                    tracking_number = task['tracking_number']
                    print(f"[STARTUP] 处理pending AI任务: {tracking_number} (ID: {task_id[:8]})")
                    
                    # 立即处理这个任务
                    self._process_ai_task_immediately(task)
            else:
                print("[STARTUP] 没有发现pending AI分析任务")
                
        except Exception as e:
            print(f"[ERROR] 处理已有pending AI任务失败: {e}")
            import traceback
            traceback.print_exc()
    
    def stop(self):
        """停止定时任务处理器"""
        if not self.running:
            print("[WARNING] 定时任务处理器未运行")
            return
        
        print("[STOP] 停止定时任务处理器...")
        self.running = False
        
        # 取消定时器
        # 写入一次心跳，标记已停止
        try:
            self._write_heartbeat(running=False, status_msg="stopped")
        except Exception:
            pass
        if self.scraping_timer:
            self.scraping_timer.cancel()
            self.scraping_timer = None
        
        # AI定时器已移除，不再需要取消
        
        if self.status_timer:
            self.status_timer.cancel()
            self.status_timer = None
        
        # 停止执行器
        self.scraping_executor.stop()
        self.ai_executor.stop()

        print("[SUCCESS] 定时任务处理器已停止")
    
    def _scraping_task_handler(self):
        """网页抓取任务处理器"""
        if not self.running:
            return
        
        try:
            # 检查可用的处理槽位
            available_slots = self.max_scraping_tasks - len(self.active_scraping_tasks)
            if available_slots <= 0:
                # print(f"[SCRAPING] 无可用槽位，跳过本次检查 ({len(self.active_scraping_tasks)}/{self.max_scraping_tasks})")
                self._schedule_scraping_timer()
                return
            
            # 获取待处理的抓取任务
            pending_tasks = self.task_manager.get_pending_tasks_by_stage('scraping', limit=available_slots)
            
            if not pending_tasks:
                # print(f"[SCRAPING] 无待处理任务")
                self._schedule_scraping_timer()
                return
            
            print(f"[SCRAPING] 发现 {len(pending_tasks)} 个待处理抓取任务")
            
            # 处理每个任务
            for task in pending_tasks:
                if len(self.active_scraping_tasks) >= self.max_scraping_tasks:
                    break
                
                task_id = task['id']
                task_name = task['task_name']
                print(f"[SCRAPING] 开始处理任务: {task_name} (ID: {task_id})")
                
                # 更新任务状态为处理中
                self.task_manager.update_task_status(task_id, "processing")

                # 同步更新货运记录状态为"处理中"
                self._update_shipment_status_from_task(task, "处理中")

                # 记录活跃任务
                self.active_scraping_tasks[task_id] = {
                    'task': task,
                    'start_time': datetime.now(),
                    'thread': None
                }
                
                # 启动处理线程
                thread = threading.Thread(
                    target=self._process_scraping_task,
                    args=(task,),
                    name=f"Scraping-{task_id[:8]}"
                )
                thread.daemon = True
                thread.start()
                self.active_scraping_tasks[task_id]['thread'] = thread
                
                print(f"[SCRAPING] 任务已分派到线程: {thread.name}")
        
        except Exception as e:
            print(f"[ERROR] 网页抓取任务处理器异常: {e}")
            traceback.print_exc()
        
        # 重新调度定时器
        self._schedule_scraping_timer()
    
    def _status_update_handler(self):
        """状态更新处理器 - 定期检查和清理任务状态"""
        if not self.running:
            return
        
        try:
            # 清理已完成的活跃任务记录
            self._cleanup_completed_tasks()
            
            # 检查孤立任务（长时间处理中但无活跃记录的任务）
            self._check_orphaned_tasks()
            
            # 定期检查是否有pending的AI分析任务需要处理
            self._check_and_process_pending_ai_tasks()
            
            # 写入心跳信息
            self._write_heartbeat(running=True, status_msg="running")
            
        except Exception as e:
            print(f"[ERROR] 状态更新处理器异常: {e}")
            traceback.print_exc()
        
        # 重新调度定时器
        self._schedule_status_timer()
    
    def _check_and_process_pending_ai_tasks(self):
        """定期检查并处理pending的AI分析任务"""
        try:
            # 检查是否有可用的AI处理槽位
            available_slots = self.max_ai_tasks - len(self.active_ai_tasks)
            if available_slots <= 0:
                return
            
            # 获取pending的AI分析任务
            pending_ai_tasks = self.task_manager.get_pending_tasks_by_stage('ai_analysis', limit=available_slots)
            
            if pending_ai_tasks:
                print(f"[STATUS] 发现 {len(pending_ai_tasks)} 个pending AI分析任务需要处理")
                
                for task in pending_ai_tasks:
                    if len(self.active_ai_tasks) >= self.max_ai_tasks:
                        break
                    
                    task_id = task['id']
                    tracking_number = task['tracking_number']
                    print(f"[STATUS] 处理pending AI任务: {tracking_number} (ID: {task_id[:8]})")
                    
                    # 立即处理这个任务
                    self._process_ai_task_immediately(task)
                    
        except Exception as e:
            print(f"[ERROR] 检查pending AI任务失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _update_shipment_status_from_task(self, task: Dict, new_status: str):
        """从任务信息中提取货运记录ID并更新状态"""
        try:
            print(f"[DEBUG] 开始状态更新，任务ID: {task.get('id', 'Unknown')[:8]}")
            remarks = task.get('remarks', '')
            print(f"[DEBUG] 任务备注: {remarks}")

            if '货运记录ID:' in remarks:
                record_id = remarks.split('货运记录ID:')[1].split(',')[0].strip()
                print(f"[STATUS] 更新货运记录 {record_id} 状态为: {new_status}")

                # 查询当前记录状态，若已完成则跳过更新，避免干扰历史记录
                try:
                    import sqlite3
                    conn = sqlite3.connect('db/shipment_records.db')
                    cur = conn.cursor()
                    cur.execute('SELECT status FROM shipment_records WHERE id = ?', (record_id,))
                    row = cur.fetchone()
                    conn.close()
                    if row and row[0] == '已完成':
                        print(f"[INFO] 记录 {record_id} 已完成，跳过状态更新")
                        return
                except Exception:
                    pass

                # 直接更新数据库中的货运记录状态（脱离UI也能生效）
                try:
                    updated = self.shipment_manager.update_shipment_status(record_id, new_status, 'task_status_sync')
                    if updated:
                        print(f"[SUCCESS] 货运记录 {record_id} 状态已更新为: {new_status}")
                    else:
                        print(f"[WARNING] 更新货运记录状态失败或未变更: {record_id} -> {new_status}")
                except Exception as e:
                    print(f"[ERROR] 直接更新货运记录状态异常: {e}")
                    import traceback
                    traceback.print_exc()

                # 同时（如果有）通过回调通知UI刷新
                if self.completion_callback:
                    status_update_data = {
                        'shipment_status_update': {
                            'record_id': record_id,
                            'new_status': new_status
                        }
                    }
                    try:
                        print(f"[DEBUG] 调用状态更新回调...")
                        self.completion_callback(task['id'], 'status_update', status_update_data)
                        print(f"[DEBUG] 状态更新回调已调用")
                    except Exception as e:
                        print(f"[WARNING] 状态更新回调失败: {e}")
                        import traceback
                        traceback.print_exc()
            else:
                print(f"[WARNING] 任务备注中未找到货运记录ID")
        except Exception as e:
            print(f"[ERROR] 从任务更新货运记录状态失败: {e}")
            import traceback
            traceback.print_exc()

    def _process_scraping_task(self, task: Dict):
        """处理单个网页抓取任务"""
        task_id = task['id']
        tracking_number = task['tracking_number']
        
        try:
            print(f"[SCRAPING] 执行网页抓取: {tracking_number}")
            
            # 调用抓取执行器
            result = self.scraping_executor.execute_task(task)
            
            if result and result.get('success'):
                # 任务成功完成
                self.task_manager.update_task_status(
                    task_id, 
                    "completed",
                    result_summary=result.get('summary', '抓取完成')
                )
                print(f"[SUCCESS] 网页抓取任务完成: {tracking_number}")
                
                # 调用完成回调
                if self.completion_callback:
                    try:
                        self.completion_callback(task_id, 'scraping', result.get('data', {}))
                    except Exception as callback_error:
                        print(f"[WARNING] 回调函数执行失败: {callback_error}")
                
                # 立即创建和处理AI分析任务（恢复顺序处理）
                result_data = result.get('data', {})
                actual_raw_data_path = result_data.get('raw_data_path')
                
                # 如果抓取结果中没有路径，尝试查找最新的数据文件夹
                if not actual_raw_data_path:
                    from utils.file_manager import get_file_manager
                    file_mgr = get_file_manager()
                    tracking_files = file_mgr.get_files_for_tracking(tracking_number)
                    if tracking_files:
                        actual_raw_data_path = tracking_files[0]['folder_path']
                        print(f"[AI] 未找到抓取结果路径，使用最新数据文件夹: {actual_raw_data_path}")
                    else:
                        print(f"[WARNING] 未找到跟踪号 {tracking_number} 的任何数据文件夹，跳过AI分析")
                        return
                
                self._create_and_process_ai_task_for_scraping(task_id, tracking_number, task, actual_raw_data_path)
                
            else:
                # 任务失败
                error_msg = result.get('error', '抓取失败') if result else '抓取失败'
                self.task_manager.update_task_status(
                    task_id,
                    "failed",
                    error_message=error_msg,
                    result_summary="抓取失败"
                )
                print(f"[ERROR] 网页抓取任务失败: {tracking_number} - {error_msg}")
        
        except Exception as e:
            error_msg = f"抓取任务异常: {str(e)}"
            print(f"[ERROR] {error_msg}")
            traceback.print_exc()
            
            self.task_manager.update_task_status(
                task_id,
                "failed", 
                error_message=error_msg,
                result_summary="抓取异常"
            )
        
        finally:
            # 从活跃任务列表中移除
            if task_id in self.active_scraping_tasks:
                del self.active_scraping_tasks[task_id]
                print(f"[SCRAPING] 任务已完成，从活跃列表移除: {task_id}")
    
    def _create_and_process_ai_task_for_scraping(self, parent_task_id: str, tracking_number: str, parent_task: Dict, raw_data_path: str):
        """为完成的抓取任务立即创建并处理AI分析任务"""
        try:
            print(f"[AI] 为完成的抓取任务创建AI分析任务: {tracking_number}")
            print(f"[AI] 使用数据路径: {raw_data_path}")
            
            # 从父任务获取货运记录ID
            parent_remarks = parent_task.get('remarks', '')
            shipment_record_id = None
            if '货运记录ID:' in parent_remarks:
                shipment_record_id = parent_remarks.split('货运记录ID:')[1].split(',')[0].strip()
                print(f"[AI] 从父任务备注中提取货运记录ID: {shipment_record_id}")
            
            # 创建AI分析任务的备注
            ai_remarks = f"跟随抓取任务自动创建的AI分析任务，父任务: {parent_task_id}"
            if shipment_record_id:
                ai_remarks += f", 货运记录ID: {shipment_record_id}"
                
            # 创建AI分析任务
            ai_task_id = self.task_manager.create_ai_analysis_task(
                parent_task_id=parent_task_id,
                raw_data_path=raw_data_path,
                priority=0,
                remarks=ai_remarks
            )
            
            if ai_task_id:
                print(f"[AI] AI分析任务创建成功: {ai_task_id}")
                
                # 获取AI任务详情并立即处理
                ai_tasks = self.task_manager.get_pending_tasks_by_stage('ai_analysis', limit=1)
                ai_task = None
                for task in ai_tasks:
                    if task['id'] == ai_task_id:
                        ai_task = task
                        break
                
                if ai_task:
                    print(f"[AI] 立即处理AI分析任务: {tracking_number}")
                    self._process_ai_task_immediately(ai_task)
                else:
                    print(f"[WARNING] 无法找到刚创建的AI任务: {ai_task_id}")
            else:
                print(f"[ERROR] AI分析任务创建失败: {tracking_number}")
                
        except Exception as e:
            print(f"[ERROR] 创建AI任务失败: {e}")
            traceback.print_exc()
    
    def _process_ai_task_immediately(self, task: Dict):
        """立即处理AI分析任务（不通过定时器）"""
        task_id = task['id']
        tracking_number = task['tracking_number']
        
        try:
            print(f"[AI] 立即执行AI分析: {tracking_number}")
            
            # 更新任务状态为处理中
            self.task_manager.update_task_status(task_id, "processing")
            
            # 调用AI分析执行器
            result = self.ai_executor.execute_task(task)
            
            if result and result.get('success'):
                # 任务成功完成
                self.task_manager.update_task_status(
                    task_id,
                    "completed", 
                    result_summary=result.get('summary', 'AI分析完成')
                )
                print(f"[SUCCESS] AI分析任务完成: {tracking_number}")
                
                # 同时更新货运记录状态为"已完成"（即使没有UI回调也能落库）
                self._update_shipment_status_from_task(task, "已完成")

                # 调用完成回调
                if self.completion_callback:
                    try:
                        result_data = result.get('data', {})
                        print(f"[AI] 准备调用完成回调，result_data keys: {list(result_data.keys()) if result_data else 'None'}")
                        if result_data:
                            print(f"[AI] ETA: {result_data.get('estimated_arrival_time', 'None')}")
                            print(f"[AI] 日期数据: {len(result_data.get('dates_data', []))} 条")
                        self.completion_callback(task_id, 'ai_analysis', result_data)
                    except Exception as callback_error:
                        print(f"[WARNING] 回调函数执行失败: {callback_error}")
                        import traceback
                        traceback.print_exc()
                
            else:
                # 任务失败
                error_msg = result.get('error', 'AI分析失败') if result else 'AI分析失败'
                self.task_manager.update_task_status(
                    task_id,
                    "failed",
                    error_message=error_msg,
                    result_summary="AI分析失败"
                )
                print(f"[ERROR] AI分析任务失败: {tracking_number} - {error_msg}")
        
        except Exception as e:
            error_msg = f"AI分析任务异常: {str(e)}"
            print(f"[ERROR] {error_msg}")
            traceback.print_exc()
            
            self.task_manager.update_task_status(
                task_id,
                "failed",
                error_message=error_msg, 
                result_summary="AI分析异常"
            )
    
    def _process_ai_task(self, task: Dict):
        """处理单个AI分析任务"""
        task_id = task['id']
        tracking_number = task['tracking_number']
        
        try:
            print(f"[AI] 执行AI分析: {tracking_number}")
            
            # 调用AI分析执行器
            result = self.ai_executor.execute_task(task)

            if result and result.get('success'):
                # 任务成功完成
                self.task_manager.update_task_status(
                    task_id,
                    "completed",
                    result_summary=result.get('summary', 'AI分析完成')
                )
                print(f"[SUCCESS] AI分析任务完成: {tracking_number}")

                # 同步更新货运记录状态为"已完成"
                self._update_shipment_status_from_task(task, "已完成")

                # 调用完成回调
                if self.completion_callback:
                    try:
                        self.completion_callback(task_id, 'ai_analysis', result.get('data', {}))
                    except Exception as callback_error:
                        print(f"[WARNING] 回调函数执行失败: {callback_error}")

            else:
                # 任务失败
                error_msg = result.get('error', 'AI分析失败') if result else 'AI分析失败'
                self.task_manager.update_task_status(
                    task_id,
                    "failed",
                    error_message=error_msg,
                    result_summary="AI分析失败"
                )
                print(f"[ERROR] AI分析任务失败: {tracking_number} - {error_msg}")
        
        except Exception as e:
            error_msg = f"AI分析任务异常: {str(e)}"
            print(f"[ERROR] {error_msg}")
            traceback.print_exc()
            
            self.task_manager.update_task_status(
                task_id,
                "failed",
                error_message=error_msg, 
                result_summary="AI分析异常"
            )
        
        finally:
            # 从活跃任务列表中移除
            if task_id in self.active_ai_tasks:
                del self.active_ai_tasks[task_id]
                print(f"[AI] 任务已完成，从活跃列表移除: {task_id}")
    
    def _cleanup_completed_tasks(self):
        """清理已完成的活跃任务记录"""
        # 清理网页抓取任务
        completed_scraping = []
        for task_id, info in self.active_scraping_tasks.items():
            thread = info.get('thread')
            if thread and not thread.is_alive():
                completed_scraping.append(task_id)
        
        for task_id in completed_scraping:
            del self.active_scraping_tasks[task_id]
            print(f"[CLEANUP] 清理完成的抓取任务: {task_id}")
        
        # 清理AI分析任务
        completed_ai = []
        for task_id, info in self.active_ai_tasks.items():
            thread = info.get('thread')
            if thread and not thread.is_alive():
                completed_ai.append(task_id)
        
        for task_id in completed_ai:
            del self.active_ai_tasks[task_id]
            print(f"[CLEANUP] 清理完成的AI任务: {task_id}")
    
    def _check_orphaned_tasks(self):
        """检查孤立任务（长时间处理中但无活跃记录）"""
        try:
            # 查找长时间处理中的任务
            timeout_threshold = datetime.now() - timedelta(minutes=30)  # 30分钟超时
            
            conn = sqlite3.connect('db/task_queue.db')
            cursor = conn.cursor()
            
            # 查找超时的处理中任务
            cursor.execute("""
                SELECT id, task_name, started_at
                FROM task_queue
                WHERE status = 'processing' AND started_at < ?
            """, (timeout_threshold.isoformat(),))
            
            orphaned_tasks = cursor.fetchall()
            conn.close()
            
            for task_id, task_name, started_at in orphaned_tasks:
                # 检查是否在活跃列表中
                if (task_id not in self.active_scraping_tasks and 
                    task_id not in self.active_ai_tasks):
                    print(f"[WARNING] 发现孤立任务: {task_name} (ID: {task_id})")
                    # 重置为pending状态
                    self.task_manager.update_task_status(
                        task_id, 
                        "pending",
                        error_message="任务超时，重置为待处理"
                    )
                    print(f"[RESET] 孤立任务已重置: {task_id}")
            
        except Exception as e:
            print(f"[ERROR] 检查孤立任务失败: {e}")
    
    def _schedule_scraping_timer(self):
        """重新调度网页抓取定时器"""
        if self.running:
            self.scraping_timer = threading.Timer(self.scraping_interval, self._scraping_task_handler)
            self.scraping_timer.daemon = True
            self.scraping_timer.start()
    
    def _schedule_status_timer(self):
        """重新调度状态更新定时器"""
        if self.running:
            self.status_timer = threading.Timer(self.status_update_interval, self._status_update_handler)
            self.status_timer.daemon = True
            self.status_timer.start()
    
    def get_status(self) -> Dict:
        """获取处理器状态"""
        return {
            'running': self.running,
            'mode': 'scheduled',
            'scraping': {
                'active_tasks_count': len(self.active_scraping_tasks),
                'max_concurrent_tasks': self.max_scraping_tasks,
                'check_interval': self.scraping_interval,
                'active_tasks': {
                    task_id: {
                        'task_name': info['task']['task_name'],
                        'start_time': info['start_time'].isoformat(),
                        'duration_seconds': (datetime.now() - info['start_time']).total_seconds()
                    }
                    for task_id, info in self.active_scraping_tasks.items()
                }
            },
            'ai_analysis': {
                'active_tasks_count': len(self.active_ai_tasks),
                'max_concurrent_tasks': self.max_ai_tasks,
                'check_interval': self.ai_interval,
                'active_tasks': {
                    task_id: {
                        'task_name': info['task']['task_name'],
                        'start_time': info['start_time'].isoformat(),
                        'duration_seconds': (datetime.now() - info['start_time']).total_seconds()
                    }
                    for task_id, info in self.active_ai_tasks.items()
                }
            }
        }
    
    def _write_heartbeat(self, running: bool = True, status_msg: str = "running"):
        """写入心跳信息（可选的状态跟踪）"""
        try:
            import json
            from datetime import datetime
            from pathlib import Path
            
            # 确保db目录存在
            db_dir = Path("db")
            db_dir.mkdir(exist_ok=True)
            
            # 心跳数据
            heartbeat_data = {
                "running": running,
                "status": status_msg,
                "timestamp": datetime.now().isoformat(),
                "active": {
                    "scraping_tasks": getattr(self.scraping_executor, "active_tasks_count", 0) if hasattr(self, 'scraping_executor') else 0,
                    "ai_tasks": getattr(self.ai_executor, "active_tasks_count", 0) if hasattr(self, 'ai_executor') else 0
                },
                "intervals": {
                    "scraping": getattr(self, "scraping_interval", 0),
                    "ai": getattr(self, "ai_interval", 0),
                    "status_update": getattr(self, "status_update_interval", 0)
                },
                "stats": {
                    "max_scraping_tasks": getattr(self, "max_scraping_tasks", 0),
                    "max_ai_tasks": getattr(self, "max_ai_tasks", 0)
                }
            }
            
            # 写入心跳文件
            heartbeat_file = db_dir / "processor_heartbeat.json"
            with heartbeat_file.open("w", encoding="utf-8") as f:
                json.dump(heartbeat_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            # 心跳写入失败不应该影响主流程
            print(f"[WARNING] 写入心跳失败: {e}")


def run_scheduled_processor_daemon():
    """以守护进程模式运行定时任务处理器"""
    processor = ScheduledTaskProcessor(
        scraping_interval=10,      # 10秒检查一次网页抓取任务
        ai_interval=5,             # 5秒检查一次AI分析任务
        status_update_interval=30, # 30秒进行一次状态维护
        max_scraping_tasks=2,      # 最多2个并发抓取任务
        max_ai_tasks=3             # 最多3个并发AI分析任务
    )
    
    try:
        processor.start()
        print("🚀 定时任务处理器已启动 (按 Ctrl+C 停止)")
        print("=" * 60)
        
        while True:
            time.sleep(15)  # 每15秒显示一次状态
            
            status = processor.get_status()
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            print(f"\n[TIME] {current_time}")
            print(f"[网页抓取] 活跃任务: {status['scraping']['active_tasks_count']}/{status['scraping']['max_concurrent_tasks']}")
            print(f"[AI分析] 活跃任务: {status['ai_analysis']['active_tasks_count']}/{status['ai_analysis']['max_concurrent_tasks']}")
            
            # 显示活跃任务详情
            if status['scraping']['active_tasks']:
                print("[网页抓取] 正在处理:")
                for task_id, task_info in status['scraping']['active_tasks'].items():
                    print(f"  • {task_info['task_name']} (运行 {task_info['duration_seconds']:.0f}秒)")
            
            if status['ai_analysis']['active_tasks']:
                print("[AI分析] 正在处理:")
                for task_id, task_info in status['ai_analysis']['active_tasks'].items():
                    print(f"  • {task_info['task_name']} (运行 {task_info['duration_seconds']:.0f}秒)")
    
    except KeyboardInterrupt:
        print("\n[STOP] 收到停止信号，正在关闭定时任务处理器...")
    finally:
        processor.stop()
        print("👋 定时任务处理器已关闭")


if __name__ == "__main__":
    run_scheduled_processor_daemon()