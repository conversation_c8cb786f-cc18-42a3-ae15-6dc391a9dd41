// 在浏览器Console中执行此脚本来自动登录
localStorage.setItem('auth_data', '{"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiVEVTVDAwMSIsIm5hbWUiOiJcdTZkNGJcdThiZDVcdTc1MjhcdTYyMzciLCJyb2xlIjoiXHU2NjZlXHU5MDFhXHU3NTI4XHU2MjM3IiwiZXhwIjoxNzU3MTcyOTc4LCJpYXQiOjE3NTY1NjgxNzgsInR5cGUiOiJhY2Nlc3NfdG9rZW4ifQ.T0JN4V5ii6sKybtmZf2k0lp9WbR-gpLi18Nr9k7wtKc", "user": {"user_id": "TEST001", "name": "测试用户", "role": "普通用户", "avatar": "T", "status": "active", "permissions": ["query:create", "query:view", "batch:create"], "created_at": "2025-01-20T00:00:00Z", "last_login": "2025-08-30T15:36:18.990074", "query_count": 5, "success_rate": 80.0}}');
console.log('✅ 登录信息已保存');
console.log('🔄 正在刷新页面...');
location.reload();