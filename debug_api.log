[QUICK CREATE] 路由被调用，container_number: FULLTEST001
[ROUTER DEBUG] 用户 李乐 快速创建任务: FULLTEST001
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='task_4b43339d491a' container_number='FULLTEST001' carrier_code=None status='pending' priority='normal' user_id='001' created_at=datetime.datetime(2025, 8, 28, 8, 32, 58, 704235) updated_at=datetime.datetime(2025, 8, 28, 8, 32, 58, 704239) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 测试用户 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='task_fc62d964c81f' container_number='MEDUJ0618622' carrier_code='MSC' status='pending' priority='normal' user_id='TEST001' created_at=datetime.datetime(2025, 8, 28, 14, 41, 33, 204930) updated_at=datetime.datetime(2025, 8, 28, 14, 41, 33, 204959) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0616089
[ROUTER DEBUG] 用户 测试用户 快速创建任务: MEDUJ0616089
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='task_926bbcdc483c' container_number='MEDUJ0616089' carrier_code='MSC' status='pending' priority='normal' user_id='TEST001' created_at=datetime.datetime(2025, 8, 28, 15, 41, 44, 539456) updated_at=datetime.datetime(2025, 8, 28, 15, 41, 44, 539461) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0616089
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0616089
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='task_a7d057bdb370' container_number='MEDUJ0616089' carrier_code='MSC' status='pending' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 10, 8, 4, 121662) updated_at=datetime.datetime(2025, 8, 29, 10, 8, 4, 121677) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUFX601265
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUFX601265
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='task_b43c3709d0e8' container_number='MEDUFX601265' carrier_code='MSC' status='pending' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 15, 53, 54, 282720) updated_at=datetime.datetime(2025, 8, 29, 15, 53, 54, 282728) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUFX601265
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUFX601265
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[QUICK CREATE] 路由被调用，container_number: MEDUFX601265
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUFX601265
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[QUICK CREATE] 路由被调用，container_number: MEDUFX601265
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUFX601265
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[QUICK CREATE] 路由被调用，container_number: MEDUFX601265
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUFX601265
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[QUICK CREATE] 路由被调用，container_number: MEDUFX601265
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUFX601265
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[QUICK CREATE] 路由被调用，container_number: MEDUFX601265
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUFX601265
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[QUICK CREATE] 路由被调用，container_number: MEDUFX601265
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUFX601265
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='task_401a369c3dee' container_number='MEDUFX601265' carrier_code='MSC' status='pending' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 16, 40, 24, 108970) updated_at=datetime.datetime(2025, 8, 29, 16, 40, 24, 108974) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='task_8900e970b872' container_number='MEDUJ0618622' carrier_code='MSC' status='pending' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 16, 41, 26, 46585) updated_at=datetime.datetime(2025, 8, 29, 16, 41, 26, 46591) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='593ad1c9-e1da-4ca1-9e40-8b9a418bba9c' container_number='MEDUJ0618622' carrier_code='MSC' status='completed' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 16, 48, 26, 388793) updated_at=datetime.datetime(2025, 8, 29, 16, 48, 26, 388804) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUFX601265
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUFX601265
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='c22650c1-86f6-4851-96d6-7faf871e7074' container_number='MEDUFX601265' carrier_code='MSC' status='completed' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 17, 10, 55, 5974) updated_at=datetime.datetime(2025, 8, 29, 17, 10, 55, 5980) result=None
[QUICK CREATE] 路由被调用，container_number: HTTP0829171947
[ROUTER DEBUG] 用户 测试用户 快速创建任务: HTTP0829171947
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='2fb75e1f-6e77-4556-a56d-a4e454459bf7' container_number='HTTP0829171947' carrier_code='PIL' status='pending' priority='normal' user_id='TEST001' created_at=datetime.datetime(2025, 8, 29, 17, 19, 47, 720959) updated_at=datetime.datetime(2025, 8, 29, 17, 19, 47, 720963) result=None
[QUICK CREATE] 路由被调用，container_number: FIX0829172211
[ROUTER DEBUG] 用户 测试用户 快速创建任务: FIX0829172211
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='2d73b34e-398b-4fda-9fc2-455d82feb3e6' container_number='FIX0829172211' carrier_code=None status='pending' priority='normal' user_id='TEST001' created_at=datetime.datetime(2025, 8, 29, 17, 22, 12, 27214) updated_at=datetime.datetime(2025, 8, 29, 17, 22, 12, 27217) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0616089
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0616089
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='67286868-9387-4a50-9112-1cb2e76217c1' container_number='MEDUJ0616089' carrier_code='MSC' status='completed' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 17, 24, 12, 918524) updated_at=datetime.datetime(2025, 8, 29, 17, 24, 12, 918527) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0616089
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0616089
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='67286868-9387-4a50-9112-1cb2e76217c1' container_number='MEDUJ0616089' carrier_code='MSC' status='completed' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 17, 24, 34, 138139) updated_at=datetime.datetime(2025, 8, 29, 17, 24, 34, 138148) result=None
[QUICK CREATE] 路由被调用，container_number: DEBUG1756476164487
[ROUTER DEBUG] 用户 王五 快速创建任务: DEBUG1756476164487
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='1aabb576-60de-414c-9a79-7fa171d59ef7' container_number='DEBUG1756476164487' carrier_code=None status='pending' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 22, 2, 44, 687590) updated_at=datetime.datetime(2025, 8, 29, 22, 2, 44, 687595) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0616089
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0616089
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='67286868-9387-4a50-9112-1cb2e76217c1' container_number='MEDUJ0616089' carrier_code='MSC' status='completed' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 22, 8, 24, 475866) updated_at=datetime.datetime(2025, 8, 29, 22, 8, 24, 475883) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='593ad1c9-e1da-4ca1-9e40-8b9a418bba9c' container_number='MEDUJ0618622' carrier_code='MSC' status='completed' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 22, 13, 53, 596480) updated_at=datetime.datetime(2025, 8, 29, 22, 13, 53, 596492) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 测试用户 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='593ad1c9-e1da-4ca1-9e40-8b9a418bba9c' container_number='MEDUJ0618622' carrier_code='MSC' status='completed' priority='normal' user_id='TEST001' created_at=datetime.datetime(2025, 8, 29, 22, 31, 4, 165452) updated_at=datetime.datetime(2025, 8, 29, 22, 31, 4, 165462) result=None
[QUICK CREATE] 路由被调用，container_number: NEWTEST1756478063
[ROUTER DEBUG] 用户 测试用户 快速创建任务: NEWTEST1756478063
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='8fe8406a-6276-4565-b360-70ba2ce222f9' container_number='NEWTEST1756478063' carrier_code=None status='pending' priority='normal' user_id='TEST001' created_at=datetime.datetime(2025, 8, 29, 22, 34, 23, 746678) updated_at=datetime.datetime(2025, 8, 29, 22, 34, 23, 746687) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 测试用户 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='5814e936-76be-4cdd-b32e-264667bd9cd6' container_number='MEDUJ0618622' carrier_code='MSC' status='pending' priority='normal' user_id='TEST001' created_at=datetime.datetime(2025, 8, 29, 22, 40, 14, 176204) updated_at=datetime.datetime(2025, 8, 29, 22, 40, 14, 176207) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0616089
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0616089
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='36b2f923-7df8-49c6-b140-c34c5485cfd5' container_number='MEDUJ0616089' carrier_code='MSC' status='pending' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 22, 42, 10, 294554) updated_at=datetime.datetime(2025, 8, 29, 22, 42, 10, 294557) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 测试用户 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='cf929b8b-3b63-4e46-aecd-7e7fc5730520' container_number='MEDUJ0618622' carrier_code='MSC' status='pending' priority='normal' user_id='TEST001' created_at=datetime.datetime(2025, 8, 29, 22, 53, 0, 133209) updated_at=datetime.datetime(2025, 8, 29, 22, 53, 0, 133213) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUFX601265
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUFX601265
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='bb4ec65c-7397-4eb0-8070-cef83de54336' container_number='MEDUFX601265' carrier_code='MSC' status='pending' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 22, 55, 6, 587706) updated_at=datetime.datetime(2025, 8, 29, 22, 55, 6, 587714) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='d95ca77a-1e09-473f-83a3-3c93bc205d99' container_number='MEDUJ0618622' carrier_code='MSC' status='pending' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 22, 56, 29, 557032) updated_at=datetime.datetime(2025, 8, 29, 22, 56, 29, 557037) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0616089
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0616089
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='d58c2cc3-b932-4bfd-baae-68020f10ba0a' container_number='MEDUJ0616089' carrier_code='MSC' status='pending' priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 29, 23, 19, 4, 289233) updated_at=datetime.datetime(2025, 8, 29, 23, 19, 4, 289236) result=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='57499191-2cff-48b3-946a-70ea995a4780' container_number='MEDUJ0618622' carrier_code='MSC' carrier_name=None status='pending' shipment_status=None priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 30, 22, 8, 22, 377061) updated_at=datetime.datetime(2025, 8, 30, 22, 8, 22, 377064) result=None estimated_arrival_time=None evidence_screenshot=None shipment_dates=[] ai_analysis_result=None scraping_status=None ai_status=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='508d5d8f-f032-4234-b814-bdfe9445b501' container_number='MEDUJ0618622' carrier_code='MSC' carrier_name=None status='pending' shipment_status=None priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 30, 22, 9, 38, 291802) updated_at=datetime.datetime(2025, 8, 30, 22, 9, 38, 291820) result=None estimated_arrival_time=None evidence_screenshot=None shipment_dates=[] ai_analysis_result=None scraping_status=None ai_status=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0618622
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0618622
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='9b2ea459-d657-49c7-8e2d-09911eb56efc' container_number='MEDUJ0618622' carrier_code='MSC' carrier_name=None status='pending' shipment_status=None priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 30, 22, 15, 50, 85463) updated_at=datetime.datetime(2025, 8, 30, 22, 15, 50, 85486) result=None estimated_arrival_time=None evidence_screenshot=None shipment_dates=[] ai_analysis_result=None scraping_status=None ai_status=None
[QUICK CREATE] 路由被调用，container_number: MEDUFX601265
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUFX601265
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='551e5417-f384-4628-9267-bbb6b0011a8f' container_number='MEDUFX601265' carrier_code='MSC' carrier_name=None status='pending' shipment_status=None priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 30, 23, 22, 18, 552892) updated_at=datetime.datetime(2025, 8, 30, 23, 22, 18, 552898) result=None estimated_arrival_time=None evidence_screenshot=None shipment_dates=[] ai_analysis_result=None scraping_status=None ai_status=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0616089
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0616089
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='790ec74e-9023-4642-860a-6877c480833d' container_number='MEDUJ0616089' carrier_code='MSC' carrier_name=None status='pending' shipment_status=None priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 30, 23, 24, 53, 197320) updated_at=datetime.datetime(2025, 8, 30, 23, 24, 53, 197338) result=None estimated_arrival_time=None evidence_screenshot=None shipment_dates=[] ai_analysis_result=None scraping_status=None ai_status=None
[QUICK CREATE] 路由被调用，container_number: MEDUJ0616089
[ROUTER DEBUG] 用户 王五 快速创建任务: MEDUJ0616089
[ROUTER DEBUG] 准备调用 task_service.create_single_task
[ROUTER DEBUG] task_service.create_single_task 返回: task_id='5239af02-3577-4a5c-8c34-40b4288c47a5' container_number='MEDUJ0616089' carrier_code='MSC' carrier_name=None status='pending' shipment_status=None priority='normal' user_id='003' created_at=datetime.datetime(2025, 8, 30, 23, 37, 24, 461049) updated_at=datetime.datetime(2025, 8, 30, 23, 37, 24, 461072) result=None estimated_arrival_time=None evidence_screenshot=None shipment_dates=[] ai_analysis_result=None scraping_status=None ai_status=None
