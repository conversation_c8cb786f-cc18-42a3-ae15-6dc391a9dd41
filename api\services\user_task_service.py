#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户任务服务
基于现有任务管理系统，增加用户权限和信息管理
"""

import asyncio
import json
import sys
import uuid
from pathlib import Path
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from api.models.user_schemas import (
    TaskCreateRequest,
    BatchTaskCreateRequest,
    TaskResponse,
    BatchTaskCreateResponse,
    UserInfo
)
from api.services.carrier_service import CarrierValidationService
from api.services.task_service import get_task_service

# 导入现有的任务管理器
try:
    from task_manager import TaskManager
    from utils.carrier_lookup import get_company_info
except ImportError as e:
    print(f"导入任务管理模块失败: {e}")

class UserTaskService:
    """用户任务服务类"""

    def __init__(self):
        self.task_manager = TaskManager()
        self.carrier_service = CarrierValidationService()
        self.task_service = get_task_service()


    async def create_single_task(
        self,
        request: TaskCreateRequest,
        user_info: UserInfo
    ) -> TaskResponse:
        """
        创建单个任务

        Args:
            request: 任务创建请求
            user_info: 用户信息

        Returns:
            任务响应
        """
        try:
            # 校验提单号
            validation_result = self.carrier_service.validate_tracking_number(request.container_number)

            # 确定承运人代码
            carrier_code = request.carrier_code
            if not carrier_code and validation_result.is_valid and validation_result.carrier_info:
                carrier_code = validation_result.carrier_info.code

            # 生成任务ID
            task_id = f"task_{uuid.uuid4().hex[:12]}"

            # 准备任务数据
            task_data = {
                "task_id": task_id,
                "container_number": request.container_number,
                "carrier_code": carrier_code or "UNKNOWN",
                "priority": request.priority.value,
                "user_id": user_info.user_id,
                "user_name": user_info.name,
                "callback_url": request.callback_url,
                "metadata": request.metadata or {},
                "created_at": datetime.now(),
                "status": "pending"
            }

            # 调用现有的任务管理器创建任务
            print(f"[DEBUG] create_single_task 准备调用 _create_task_in_manager")
            task_info = await self._create_task_in_manager(task_data)
            print(f"[DEBUG] _create_task_in_manager 返回结果: {task_info}")

            if not task_info:
                print(f"[DEBUG] 任务创建失败，抛出异常")
                raise Exception("任务创建失败")

            # 使用真实的任务信息
            real_task_id = task_info['task_id']
            real_status = task_info['status']

            return TaskResponse(
                task_id=real_task_id,
                container_number=request.container_number,
                carrier_code=carrier_code,
                status=real_status,
                priority=request.priority.value,
                user_id=user_info.user_id,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                result=None
            )

        except Exception as e:
            raise Exception(f"创建任务失败: {str(e)}")

    async def create_batch_tasks(
        self,
        request: BatchTaskCreateRequest,
        user_info: UserInfo
    ) -> BatchTaskCreateResponse:
        """
        批量创建任务

        Args:
            request: 批量任务创建请求
            user_info: 用户信息

        Returns:
            批量任务创建响应
        """
        created_tasks = []
        failed_tasks = []

        for i, task_request in enumerate(request.tasks):
            try:
                task_response = await self.create_single_task(task_request, user_info)
                created_tasks.append(task_response)
            except Exception as e:
                failed_tasks.append({
                    "index": i,
                    "container_number": task_request.container_number,
                    "error": str(e)
                })

        summary = {
            "total": len(request.tasks),
            "success": len(created_tasks),
            "failed": len(failed_tasks)
        }

        return BatchTaskCreateResponse(
            success=len(created_tasks) > 0,
            created_tasks=created_tasks,
            failed_tasks=failed_tasks,
            summary=summary
        )

    async def _create_task_in_manager(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        在任务管理器中创建任务（遵循正确流程：先创建货运记录，再创建任务）

        Args:
            task_data: 任务数据

        Returns:
            Dict: 包含真实任务信息的字典，如果失败返回None
        """
        try:
            print(f"[DEBUG] _create_task_in_manager 开始创建货运记录和任务: {task_data['container_number']} for user {task_data['user_id']}")
            
            # 导入货运记录管理器
            from shipment_manager import ShipmentManager
            shipment_manager = ShipmentManager()
            
            # 确定是提单号还是箱号
            container_number = task_data["container_number"]
            carrier_code = task_data.get("carrier_code", "")
            
            # 使用carrier_lookup获取完整的承运商信息，就像app.py一样
            from utils.carrier_lookup import get_company_info
            carrier_info = get_company_info(container_number)
            carrier_company = carrier_info.get('company', carrier_code) if carrier_info else carrier_code
            
            print(f"[DEBUG] 承运商信息: 原始代码={carrier_code}, 完整信息={carrier_company}")
            
            # 简单的判断逻辑：一般提单号比箱号长
            is_bill_of_lading = len(container_number) >= 12
            
            # 不检查已存在的记录，总是创建新的任务来获取最新状态
            # 因为货运状态会随时间变化，用户可能需要多次查询同一个跟踪号
            
            # 直接创建货运记录（这会自动创建关联的任务）
            record_id = shipment_manager.create_shipment_record(
                bill_of_lading=container_number if is_bill_of_lading else None,
                container_number=container_number if not is_bill_of_lading else None,
                carrier_company=carrier_company,
                created_by=task_data['user_id']  # 使用用户ID而不是用户名
            )
            print(f"[DEBUG] 货运记录创建成功，ID: {record_id}")
            
            # 获取刚创建的任务信息
            try:
                import sqlite3
                conn = sqlite3.connect('db/task_queue.db')
                cursor = conn.cursor()
                cursor.execute('SELECT id, status FROM task_queue WHERE tracking_number = ? ORDER BY created_at DESC LIMIT 1', (container_number,))
                task_row = cursor.fetchone()
                conn.close()
                
                if task_row:
                    print(f"[DEBUG] 获取到关联任务: {task_row[0]}")
                    return {
                        'task_id': task_row[0],
                        'record_id': record_id,
                        'status': task_row[1]
                    }
                else:
                    print(f"[WARNING] 货运记录创建成功但未找到关联任务")
                    return None
            except Exception as e:
                print(f"[WARNING] 查询新创建任务失败: {e}")
                return None
            
        except Exception as e:
            print(f"[DEBUG] 创建货运记录和任务失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    async def get_user_tasks(
        self,
        user_info: UserInfo,
        status_filter: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[TaskResponse]:
        """
        获取用户任务列表（增强版，包含货运记录详情）

        Args:
            user_info: 用户信息
            status_filter: 状态过滤
            limit: 限制数量
            offset: 偏移量

        Returns:
            任务响应列表
        """
        try:
            # 获取task_queue连接
            task_conn = self.task_manager._get_connection()
            task_cursor = task_conn.cursor()
            
            # 只查询scraping阶段的任务作为主任务（每个tracking_number一条记录）
            sql_parts = ["""
                SELECT * FROM task_queue 
                WHERE creator_id = ? AND task_stage = 'scraping'
            """]
            params = [user_info.user_id]
            
            if status_filter:
                # 支持逗号分隔的多状态过滤，例如 "pending,running"
                if isinstance(status_filter, str) and "," in status_filter:
                    statuses = [s.strip() for s in status_filter.split(",") if s.strip()]
                    if statuses:
                        placeholders = ",".join(["?"] * len(statuses))
                        sql_parts.append(f"AND status IN ({placeholders})")
                        params.extend(statuses)
                else:
                    sql_parts.append("AND status = ?")
                    params.append(status_filter)
                
            sql_parts.append("ORDER BY updated_at DESC")
            
            if limit:
                sql_parts.append(f"LIMIT {limit}")
                if offset:
                    sql_parts.append(f"OFFSET {offset}")
            
            sql = " ".join(sql_parts)
            print(f"[DEBUG] 执行SQL: {sql}")
            print(f"[DEBUG] 参数: {params}")
            task_cursor.execute(sql, params)
            
            tasks = []
            rows = task_cursor.fetchall()
            print(f"[DEBUG] 查询到 {len(rows)} 条scraping任务")
            
            # 获取shipment_records连接
            import sqlite3
            shipment_conn = sqlite3.connect('db/shipment_records.db')
            shipment_cursor = shipment_conn.cursor()
            
            for row in rows:
                try:
                    columns = [col[0] for col in task_cursor.description]
                    task_dict = dict(zip(columns, row))
                    
                    tracking_number = task_dict['tracking_number']
                    print(f"[DEBUG] 处理任务: {tracking_number}")
                    
                    # 解析result_summary
                    result = {}
                    if task_dict.get('result_summary'):
                        try:
                            result = json.loads(task_dict['result_summary'])
                        except json.JSONDecodeError:
                            result = {}
                    
                    # 获取对应的shipment记录
                    shipment_info = self._get_shipment_info(shipment_cursor, tracking_number, task_dict.get('remarks'))
                    
                    # 获取任务执行状态
                    scraping_status, ai_status = self._get_task_execution_status(task_cursor, tracking_number, task_dict.get('remarks'))
                    
                    # 获取物流节点
                    shipment_dates = self._get_shipment_dates(shipment_cursor, shipment_info.get('id') if shipment_info else None)
                    
                    # 解析创建时间和更新时间
                    created_at = task_dict['created_at']
                    updated_at = task_dict['updated_at']
                    if isinstance(created_at, str):
                        try:
                            created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        except:
                            created_at = datetime.now()
                    if isinstance(updated_at, str):
                        try:
                            updated_at = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                        except:
                            updated_at = datetime.now()
                    
                    # 创建 ShipmentDates 对象列表
                    formatted_dates = []
                    for date_info in shipment_dates:
                        formatted_dates.append({
                            'date_type': date_info.get('date_type', ''),
                            'planned_date': date_info.get('planned_date'),
                            'actual_date': date_info.get('actual_date'),
                            'status': date_info.get('status'),
                            'location': date_info.get('location')
                        })
                    
                    # 确定主任务状态（基于scraping和ai的综合状态）
                    main_status = self._determine_main_task_status(task_dict['status'], scraping_status, ai_status)
                    
                    tasks.append(TaskResponse(
                        task_id=task_dict['id'],
                        container_number=tracking_number,
                        carrier_code=task_dict['carrier'],
                        carrier_name=shipment_info.get('carrier_company') if shipment_info else None,
                        status=main_status,
                        shipment_status=shipment_info.get('status') if shipment_info else None,
                        priority=str(task_dict.get('priority', 'normal')),
                        user_id=task_dict['creator_id'],
                        created_at=created_at,
                        updated_at=updated_at,
                        result=result,
                        estimated_arrival_time=shipment_info.get('estimated_arrival_time') if shipment_info else None,
                        evidence_screenshot=shipment_info.get('evidence_screenshot') if shipment_info else None,
                        shipment_dates=formatted_dates,
                        ai_analysis_result=shipment_info.get('ai_analysis_result') if shipment_info else None,
                        scraping_status=scraping_status,
                        ai_status=ai_status
                    ))
                
                except Exception as row_error:
                    print(f"处理任务行失败: {row_error}")
                    print(f"问题行数据: {row}")
                    import traceback
                    traceback.print_exc()
                    continue
            
            task_conn.close()
            shipment_conn.close()
            print(f"[DEBUG] 返回 {len(tasks)} 个任务")
            return tasks
            
        except Exception as e:
            # 失败则退回空列表并记录日志
            print(f"get_user_tasks 失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _determine_main_task_status(self, scraping_task_status: str, scraping_status: str, ai_status: str) -> str:
        """确定主任务的整体状态"""
        # 如果AI分析已完成，则整体状态为completed
        if ai_status == '已完成':
            return 'completed'
        
        # 如果AI分析失败，但网页抓取成功，状态为部分完成
        if scraping_status == '已完成' and ai_status == '失败':
            return 'partially_completed'
        
        # 如果网页抓取失败，整体状态为失败
        if scraping_status == '失败':
            return 'failed'
        
        # 如果还在处理中，返回processing
        if scraping_status in ['待处理', '进行中'] or ai_status in ['待处理', '进行中']:
            return 'processing'
        
        # 默认返回scraping任务的状态
        return scraping_task_status

    def _get_shipment_info(self, shipment_cursor, tracking_number: str, remarks: str = None) -> Optional[Dict]:
        """获取货运记录信息"""
        try:
            print(f"[DEBUG] _get_shipment_info: tracking_number={tracking_number}, remarks={remarks}")
            
            # 根据tracking_number查询shipment记录
            # 如果有remarks且包含货运记录ID，则优先使用ID查询
            if remarks and '货运记录ID:' in remarks:
                try:
                    import re
                    match = re.search(r'货运记录ID:\s*(\d+)', remarks)
                    if match:
                        record_id = int(match.group(1))
                        print(f"[DEBUG] 使用记录ID查询: {record_id}")
                        shipment_cursor.execute("""
                            SELECT id, carrier_company, status, estimated_arrival_time, 
                                   evidence_screenshot, remarks, bill_of_lading, container_number
                            FROM shipment_records WHERE id = ?
                        """, (record_id,))
                        result = shipment_cursor.fetchone()
                        if result:
                            columns = ['id', 'carrier_company', 'status', 'estimated_arrival_time', 
                                     'evidence_screenshot', 'remarks', 'bill_of_lading', 'container_number']
                            shipment_info = dict(zip(columns, result))
                            print(f"[DEBUG] 通过ID找到shipment: {shipment_info}")
                            return shipment_info
                        else:
                            print(f"[DEBUG] 通过ID未找到shipment记录")
                except Exception as e:
                    print(f"[DEBUG] ID查询异常: {e}")
                    pass
            
            # 按tracking_number查询，优先选择有完整数据的记录
            print(f"[DEBUG] 使用tracking_number查询: {tracking_number}")
            shipment_cursor.execute("""
                SELECT id, carrier_company, status, estimated_arrival_time, 
                       evidence_screenshot, remarks, bill_of_lading, container_number
                FROM shipment_records 
                WHERE bill_of_lading = ? OR container_number = ?
                ORDER BY 
                    CASE WHEN estimated_arrival_time IS NOT NULL THEN 1 ELSE 2 END,
                    CASE WHEN evidence_screenshot IS NOT NULL THEN 1 ELSE 2 END,
                    updated_at DESC 
                LIMIT 1
            """, (tracking_number, tracking_number))
            
            result = shipment_cursor.fetchone()
            if result:
                columns = ['id', 'carrier_company', 'status', 'estimated_arrival_time', 
                         'evidence_screenshot', 'remarks', 'bill_of_lading', 'container_number']
                shipment_info = dict(zip(columns, result))
                print(f"[DEBUG] 通过tracking_number找到shipment: {shipment_info}")
                return shipment_info
            else:
                print(f"[DEBUG] 通过tracking_number未找到shipment记录")
            return None
        except Exception as e:
            print(f"获取shipment信息失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _get_task_execution_status(self, task_cursor, tracking_number: str, remarks: str = None) -> tuple:
        """获取任务执行状态（网页抓取和AI分析）"""
        try:
            status_mapping = {
                'pending': '待处理',
                'processing': '进行中', 
                'completed': '已完成',
                'failed': '失败'
            }
            
            scraping_status = "未开始"
            ai_status = "未开始"
            
            # 查询网页抓取任务状态
            if remarks and '货运记录ID:' in remarks:
                task_cursor.execute("""
                    SELECT status FROM task_queue 
                    WHERE tracking_number = ? AND task_stage = 'scraping' 
                      AND remarks LIKE ?
                    ORDER BY created_at DESC LIMIT 1
                """, (tracking_number, f'%{remarks}%'))
            else:
                task_cursor.execute("""
                    SELECT status FROM task_queue 
                    WHERE tracking_number = ? AND task_stage = 'scraping'
                    ORDER BY created_at DESC LIMIT 1
                """, (tracking_number,))
            
            scraping_result = task_cursor.fetchone()
            if scraping_result:
                scraping_status = status_mapping.get(scraping_result[0], scraping_result[0])
            
            # 查询AI分析任务状态
            if remarks and '货运记录ID:' in remarks:
                task_cursor.execute("""
                    SELECT status FROM task_queue 
                    WHERE tracking_number = ? AND task_stage = 'ai_analysis'
                      AND remarks LIKE ?
                    ORDER BY created_at DESC LIMIT 1
                """, (tracking_number, f'%{remarks}%'))
            else:
                task_cursor.execute("""
                    SELECT status FROM task_queue 
                    WHERE tracking_number = ? AND task_stage = 'ai_analysis'
                    ORDER BY created_at DESC LIMIT 1
                """, (tracking_number,))
                
            ai_result = task_cursor.fetchone()
            if ai_result:
                ai_status = status_mapping.get(ai_result[0], ai_result[0])
            elif scraping_status == '已完成':
                ai_status = "准备中"
            elif scraping_status in ['待处理', '进行中']:
                ai_status = "等待中"
                
            return scraping_status, ai_status
        except Exception as e:
            print(f"获取任务执行状态失败: {e}")
            return "错误", "错误"

    def _get_shipment_dates(self, shipment_cursor, shipment_id: int = None) -> List[Dict]:
        """获取物流节点"""
        if not shipment_id:
            return []
        
        try:
            shipment_cursor.execute("""
                SELECT date_type, planned_date, actual_date, status, location
                FROM shipment_dates WHERE shipment_id = ?
                ORDER BY id
            """, (shipment_id,))
            
            dates = []
            for row in shipment_cursor.fetchall():
                dates.append({
                    'date_type': row[0],
                    'planned_date': row[1],
                    'actual_date': row[2], 
                    'status': row[3],
                    'location': row[4]
                })
            return dates
        except Exception as e:
            print(f"获取物流节点失败: {e}")
            return []

    async def validate_and_create_from_text(
        self,
        text_input: str,
        user_info: UserInfo,
        priority: str = "normal",
        deduplicate: bool = True
    ) -> BatchTaskCreateResponse:
        """
        从文本解析并创建任务（一步完成）

        Args:
            text_input: 输入文本
            user_info: 用户信息
            priority: 任务优先级
            deduplicate: 是否去重

        Returns:
            批量任务创建响应
        """
        try:
            # 解析文本
            tracking_numbers = self.carrier_service.parse_batch_input(text_input)

            if not tracking_numbers:
                return BatchTaskCreateResponse(
                    success=False,
                    created_tasks=[],
                    failed_tasks=[{"error": "未解析出有效的提单号"}],
                    summary={"total": 0, "success": 0, "failed": 1}
                )

            # 去重处理
            if deduplicate:
                tracking_numbers = list(dict.fromkeys(tracking_numbers))

            # 限制数量
            if len(tracking_numbers) > 100:
                tracking_numbers = tracking_numbers[:100]

            # 创建任务请求列表
            task_requests = []
            for tracking_number in tracking_numbers:
                task_requests.append(TaskCreateRequest(
                    container_number=tracking_number,
                    priority=priority
                ))

            # 批量创建任务
            batch_request = BatchTaskCreateRequest(tasks=task_requests)
            return await self.create_batch_tasks(batch_request, user_info)

        except Exception as e:
            return BatchTaskCreateResponse(
                success=False,
                created_tasks=[],
                failed_tasks=[{"error": str(e)}],
                summary={"total": 0, "success": 0, "failed": 1}
            )

def get_user_task_service() -> UserTaskService:
    """获取用户任务服务实例"""
    return UserTaskService()